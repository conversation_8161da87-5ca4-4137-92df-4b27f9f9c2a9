"use client";

import { useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { FaExclamationTriangle, FaHome } from 'react-icons/fa';

export default function AuthErrorPage() {
  const searchParams = useSearchParams();
  const error = searchParams.get('error');

  const getErrorMessage = (error: string | null) => {
    switch (error) {
      case 'Configuration':
        return 'Der er et problem med server konfigurationen.';
      case 'AccessDenied':
        return 'Adgang nægtet. Du har ikke tilladelse til at logge ind.';
      case 'Verification':
        return 'Verifikation fejlede. Prøv venligst igen.';
      case 'Default':
      default:
        return 'Der opstod en uventet fejl under login. Prøv venligst igen.';
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center px-4">
      <div className="max-w-md w-full bg-white rounded-lg shadow-lg p-8 text-center">
        <div className="mb-6">
          <FaExclamationTriangle className="mx-auto text-red-500 text-6xl mb-4" />
          <h1 className="text-2xl font-bold text-gray-900 mb-2">Login Fejl</h1>
          <p className="text-gray-600">
            {getErrorMessage(error)}
          </p>
        </div>

        <div className="space-y-4">
          <Link
            href="/login"
            className="block w-full bg-blue-600 text-white py-3 px-4 rounded-lg font-medium hover:bg-blue-700 transition-colors duration-200"
          >
            Prøv igen
          </Link>
          
          <Link
            href="/"
            className="flex items-center justify-center w-full bg-gray-100 text-gray-700 py-3 px-4 rounded-lg font-medium hover:bg-gray-200 transition-colors duration-200"
          >
            <FaHome className="mr-2" />
            Tilbage til forsiden
          </Link>
        </div>

        {error && (
          <div className="mt-6 p-3 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-500">
              Fejl kode: <code className="font-mono">{error}</code>
            </p>
          </div>
        )}
      </div>
    </div>
  );
}
